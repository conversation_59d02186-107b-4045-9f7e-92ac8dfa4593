services:

  mysql:
    image: mysql:8.0.35
    command: --default-authentication-plugin=mysql_native_password
    ports:
      - "33306:3306"
    networks:
      database: {}
    environment:
      MYSQL_DATABASE: grupooptimo_pv360_v13
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: user
      MYSQL_PASSWORD: root
    volumes:
      - mysql:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d  # Include custom initialization scripts
    #restart: always

  phpmyadmin:
    image: phpmyadmin:latest
    #restart: always
    ports:
      - "8080:80"
    networks:
      database: {}
    environment:
      PMA_HOST: mysql
      UPLOAD_LIMIT: 100M
      PMA_ABSOLUTE_URI: ""
    depends_on:
      - mysql

volumes:
  mysql:
    driver: "local"

networks:
  optimo:
    external: true
  database:
    external: true
