    #https://github.com/sebastianskurnog/docker-php-development-environment
FROM php:7.4-apache

# Copy application files to the container
COPY . /var/www/html

# Create necessary directories and set permissions
RUN mkdir -p /var/www/html/var/cache/ \
    && mkdir -p /var/www/html/var/cache/dev \
    && mkdir -p /var/www/html/var/cache/prod \
    && mkdir -p /var/www/html/var/log \
    && chmod -R 777 /var/www/html/var/ \
    && chmod -R 777 /var/www/html/var/log \
    && chmod -R 777 /var/www/html/var/cache/ \
    && chmod -R 777 /var/www/html/var/cache/dev \
    && chmod -R 777 /var/www/html/var/cache/prod

# Copy Apache configuration files
COPY .docker/apache/000-default.conf /etc/apache2/sites-available/
COPY .docker/apache/apache2.conf /etc/apache2/

# Copy PHP configuration file
COPY .docker/php/php.ini /usr/local/etc/php/

COPY vendor/facturama/facturama-php-sdk/src/Client.php /var/www/html/config/facturama/

# Install required packages
RUN apt-get update && apt-get install -y \
    libzip-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libicu-dev \
    unzip \
    git \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd \
    && docker-php-ext-configure intl \
    && docker-php-ext-install intl \
    && docker-php-ext-install pdo_mysql \
    && docker-php-ext-install zip

# Install and enable Xdebug
RUN pecl install xdebug-3.1.6 \
    && docker-php-ext-enable xdebug

# Fix permissions on public/ and var/
RUN chmod -R 777 /var/www/html/public /var/www/html/var

# Enable mod_rewrite
RUN a2enmod rewrite

# Set ownership of the app directory to www-data
RUN chown -R www-data:www-data /var/www/html

# Allow Composer to run as root (non-interactive)
ENV COMPOSER_ALLOW_SUPERUSER=1

# 1) Install Composer binary before any composer install
RUN curl -sS https://getcomposer.org/installer | php -- \
    --install-dir=/usr/local/bin --filename=composer \
    && composer --version

# 2) Tell Git to trust our project directory (avoid "dubious ownership")
RUN git config --system --add safe.directory /var/www/html

# 3) Prepare Composer cache directory
RUN mkdir -p /var/www/.composer \
    && chown -R www-data:www-data /var/www/.composer

# Switch to www-data and install PHP dependencies
USER www-data
WORKDIR /var/www/html

# RUN composer install --no-scripts --no-interaction

# Back to root for final steps
USER root


# Run assets:install with --no-interaction flag and without database validation
# RUN php bin/console assets:install --no-interaction