<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Empresa;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Clase;

class OficialController extends AbstractController
{
    /**
     * @Route("/reporte/dashboard", name="app_oficial")
     */
    public function index(EntityManagerInterface $em): Response
    {
        // Obtén las empresas desde la base de datos
        $empresas = $em->getRepository(Empresa::class)->findAll();

        return $this->render('oficial/index.html.twig', [
            'controller_name' => 'OficialController',
            'empresas' => $empresas, // <-- pasa la variable aquí
        ]);
    }

    /**
     * @Route("/ajax/clases", name="almacen-obtener-clase")
     */
    public function obtenerClases(Request $request, EntityManagerInterface $em): Response
    {
        $idempresa = $request->query->get('idempresa');
        $clases = $em->getRepository(Clase::class)->findBy(['empresaIdempresa' => $idempresa]);
        return $this->render('almacen/clases.html.twig', [
            'clases' => $clases,
        ]);
    }
}
