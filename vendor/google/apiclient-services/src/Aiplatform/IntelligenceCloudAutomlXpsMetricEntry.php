<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class IntelligenceCloudAutomlXpsMetricEntry extends \Google\Collection
{
  protected $collection_key = 'systemLabels';
  /**
   * @var string
   */
  public $argentumMetricId;
  public $doubleValue;
  /**
   * @var string
   */
  public $int64Value;
  /**
   * @var string
   */
  public $metricName;
  protected $systemLabelsType = IntelligenceCloudAutomlXpsMetricEntryLabel::class;
  protected $systemLabelsDataType = 'array';

  /**
   * @param string
   */
  public function setArgentumMetricId($argentumMetricId)
  {
    $this->argentumMetricId = $argentumMetricId;
  }
  /**
   * @return string
   */
  public function getArgentumMetricId()
  {
    return $this->argentumMetricId;
  }
  public function setDoubleValue($doubleValue)
  {
    $this->doubleValue = $doubleValue;
  }
  public function getDoubleValue()
  {
    return $this->doubleValue;
  }
  /**
   * @param string
   */
  public function setInt64Value($int64Value)
  {
    $this->int64Value = $int64Value;
  }
  /**
   * @return string
   */
  public function getInt64Value()
  {
    return $this->int64Value;
  }
  /**
   * @param string
   */
  public function setMetricName($metricName)
  {
    $this->metricName = $metricName;
  }
  /**
   * @return string
   */
  public function getMetricName()
  {
    return $this->metricName;
  }
  /**
   * @param IntelligenceCloudAutomlXpsMetricEntryLabel[]
   */
  public function setSystemLabels($systemLabels)
  {
    $this->systemLabels = $systemLabels;
  }
  /**
   * @return IntelligenceCloudAutomlXpsMetricEntryLabel[]
   */
  public function getSystemLabels()
  {
    return $this->systemLabels;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(IntelligenceCloudAutomlXpsMetricEntry::class, 'Google_Service_Aiplatform_IntelligenceCloudAutomlXpsMetricEntry');
