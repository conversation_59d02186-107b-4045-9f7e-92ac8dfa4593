{% extends 'admin/layout.html.twig' %}

{% block titleHead %}{% endblock %}
{% block title %}Reporte de Productos{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="card my-4 shadow-sm">
    <div class="card-body">
      <h5 class="card-title h2 text-center mb-4">DASHBOARD ADMINISTRADOR</h5>
   <div class="row g-4 align-items-end mb-4">
    <div class="col-md-6">
        <label for="idempresa" class="form-label fw-bold">Empresa:</label>
        <select name="empresa" id="idempresa" class="form-select" onchange="cargaDefiltros();">
            <option value="-1">Seleccione una empresa</option>
            {% for empresa in empresas %}
                <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
            {% endfor %}
        </select>
    </div>
   
  </div>
  <!-- Agrupación de filtros -->
  <div class="row g-4 mb-4">
    <div class="col-md-12">
      <div class="card h-100 shadow-sm">
        <div class="card-body p-2" id="sucursales" style="max-height: 100%; overflow-y: scroll; "></div>
      </div>
    </div>
  </div>
<div class="d-flex justify-content-center align-items-center mb-4">
    <h4 class="fw-bold me-3">INGRESO DIARIO</h4>
    
    <!-- Contenedor con los campos alineados -->
    <div class="d-flex align-items-center">
        <!-- Campo de selección de fecha -->
        <input type="date" id="fecha-hoy" class="form-control w-auto mx-2">
        

    </div>
</div>

  <!-- Ingresos diarios -->
  <div class="row g-4 mb-4">
    <div class="col-md-4">
      <div class="card text-white bg-success shadow h-100">
        <div class="card-body text-center d-flex flex-column justify-content-center">
          <h5>VENTAS</h5>
          <h3 id="ventas-total"></h3>
          <p>Suma de VENTAS</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-white bg-dark shadow h-100">
        <div class="card-body text-center d-flex flex-column justify-content-center">
          <h5>PAGOS</h5>
          <h3 id="pagos-total"></h3>
          <p>Suma de PAGOS</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-dark bg-warning shadow h-100">
        <div class="card-body text-center d-flex flex-column justify-content-center">
          <h5>POR COBRAR</h5>
          <h3 id="por-cobrar-total"></h3>
          <p>Suma de POR COBRAR</p>
        </div>
      </div>
    </div>
  </div>
  

  <!-- Filtros de rango de fecha -->
 <div class="row g-4 mb-4 align-items-center justify-content-center">
    <!-- Etiqueta para "Rango de fecha" -->
    <div class="col-md-2 d-flex align-items-center justify-content-end py-0">
        <label for="rango" class="m-0 p-0 text-end">Rango de fecha:</label>
    </div>

    <!-- Contenedor de los campos de fecha y botón de reset -->
    <div class="col-md-5 d-flex align-items-center justify-content-center">
        <!-- Campo de inicio de rango -->
        <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="form-control date-input mx-2 mb-0" name="start" />
        
        <!-- Separador entre las fechas -->
        <span class="px-2 py-2">a</span>
        
        <!-- Campo de fin de rango -->
        <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="form-control date-input mx-2 mb-0" name="end" />
        
        <!-- Botón para resetear el rango -->
        <button class="btn btn-warning ms-2" onclick="resetRangoFechaDias()">
            <i class="fa fa-eraser" aria-hidden="true"></i>
        </button>
    </div>

    <!-- Botón de búsqueda -->
    <div class="col-md-3 text-end">
        <label class="form-label d-block invisible">Buscar</label>
        <button class="btn btn-success w-100" onclick="actualizarTodasLasGraficas()">
            <i class="fa fa-refresh me-1"></i> Actualizar Dashboard
        </button>
    </div>
</div>


  <!-- Sección de Ingresos Diarios -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fa fa-chart-pie me-2"></i>Ingresos Diarios</h5>
            <button class="btn btn-light btn-sm" onclick="buscarIngresosDiarios()">
              <i class="fa fa-search me-1"></i> Actualizar Ingresos
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row g-4">
            <div class="col-lg-4">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Ingresos por Sucursal</h6>
                  <div id="graficaSucursal" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Pagos por Tipo</h6>
                  <div id="graficaTipoPago" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Deuda por Sucursal</h6>
                  <div id="deudaTotal" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Ventas acumuladas -->
<div class="d-flex justify-content-center align-items-center mb-4">
    <h4 class="fw-bold me-3">VENTAS ACUMULADAS AÑO</h4>

    <!-- Contenedor para el select y el botón -->
    <div class="d-flex align-items-center">
        <!-- Select de año -->
        <select name="year" id="year-select" class="form-control d-inline-block ms-3" style="max-width: 150px;">
            <!-- Options will be dynamically populated here -->
        </select>


    </div>
</div>


  <!-- Sección de Ventas Anuales -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header bg-success text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fa fa-chart-bar me-2"></i>Ventas Anuales</h5>
            <button class="btn btn-light btn-sm" onclick="buscarVentasAnuales()">
              <i class="fa fa-chart-bar me-1"></i> Actualizar Ventas
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row g-4">
            <div class="col-lg-6">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Ventas Mensuales</h6>
                  <div id="sumaMontos" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Pagos por Sucursal</h6>
                  <div id="sumaPagos" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
 
  <!-- Sección de Marcas y Modelos -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header bg-info text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fa fa-tags me-2"></i>Marcas y Modelos</h5>
            <button class="btn btn-light btn-sm" onclick="buscarVentasAnuales()">
              <i class="fa fa-tags me-1"></i> Actualizar Marcas
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row g-4">
            <div class="col-lg-6">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Ventas por Marca</h6>
                  <div id="recuentoMarcas" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Modelos y Tratamientos</h6>
                  <div id="tratamientoGrafica" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

<div class="d-flex justify-content-center align-items-center mb-4">
    <h4 class="fw-bold me-3">FACTURACIÓN</h4>

    <!-- Contenedor para el select y el botón -->
    <div class="d-flex align-items-center">
        <!-- Select de año -->
        <select name="year" id="year-select-facturacion" class="form-control d-inline-block ms-3" style="max-width: 150px;">
            <!-- Options will be dynamically populated here -->
        </select>


    </div>
</div>

  </div>
   <div class="row g-4 mb-4">
    <div class="col-md-12">
      <div class="card text-white bg-success shadow h-100">
        <div class="card-body text-center d-flex flex-column justify-content-center">
         
          <h3 id="suma-im"></h3>
          <p>Suma de  ImporteS</p>
        </div>
      </div>
    </div>
  <!-- Sección de Facturación -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header bg-warning text-dark">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fa fa-file-invoice me-2"></i>Facturación</h5>
            <button class="btn btn-dark btn-sm" onclick="buscarDatosFacturacion()">
              <i class="fa fa-file-invoice me-1"></i> Actualizar Facturación
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row g-4 mb-4">
            <div class="col-lg-6">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Facturación por Estatus</h6>
                  <div id="estatus" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Facturación por Tipo</h6>
                  <div id="tio" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Facturación Mensual -->
          <div class="row g-4">
            <div class="col-12">
              <div class="card border-0 bg-light">
                <div class="card-body">
                  <h6 class="card-title text-center mb-3">Facturación Mensual</h6>
                  <div id="Sumaim" style="height: 450px; min-height: 450px;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="{{ asset('lib/apexcharts-bundle/dist/apexcharts.min.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/es.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/dataTables.buttons.min.js"></script>

<script>
// ===== FUNCIONES PRINCIPALES DE CARGA =====
function actualizarTodasLasGraficas() {
    console.log('Actualizando todas las gráficas del dashboard...');
    mostrarBarraProgreso();
    cargarGraficasSecuencialmente();
}

// ===== SISTEMA DE CARGA SECUENCIAL =====
async function cargarGraficasSecuencialmente() {
    const pasos = [
        { nombre: 'Resumen de Ingresos', funcion: cargarResumenIngresos, progreso: 20 },
        { nombre: 'Gráficas de Ingresos Diarios', funcion: cargarGraficasIngresosDiarios, progreso: 40 },
        { nombre: 'Gráficas de Ventas Anuales', funcion: cargarGraficasVentasAnuales, progreso: 60 },
        { nombre: 'Gráficas de Marcas y Modelos', funcion: cargarGraficasMarcasYModelos, progreso: 80 },
        { nombre: 'Gráficas de Facturación', funcion: cargarGraficasFacturacion, progreso: 100 }
    ];

    for (let i = 0; i < pasos.length; i++) {
        const paso = pasos[i];
        actualizarProgreso(paso.progreso, `Cargando ${paso.nombre}...`);

        try {
            await new Promise((resolve) => {
                setTimeout(() => {
                    paso.funcion();
                    resolve();
                }, 500); // Pequeña pausa entre cargas para mejor UX
            });
        } catch (error) {
            console.error(`Error al cargar ${paso.nombre}:`, error);
        }
    }

    setTimeout(() => {
        ocultarBarraProgreso();
        mostrarMensajeExito('¡Dashboard actualizado correctamente!');
    }, 1000);
}

// Función para cuando cambia la empresa
function onEmpresaChange() {
    actualizarTodasLasGraficas();
}

// ===== FUNCIONES ESPECÍFICAS PARA CADA BOTÓN =====
function buscarIngresosDiarios() {
    console.log('Buscando ingresos diarios...');

    // Validar que se hayan seleccionado fechas y sucursales
    const fechaHoy = document.getElementById('fecha-hoy').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')];

    if (!fechaHoy) {
        mostrarAlertaAdvertencia('Por favor, seleccione una fecha.');
        return;
    }

    if (sucursales.length === 0) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Cargar datos
    cargarResumenIngresos();
    cargarGraficasIngresosDiarios();
    mostrarMensajeExito('Ingresos diarios actualizados correctamente');
}

function buscarVentasAnuales() {
    console.log('Buscando ventas anuales...');

    // Validar que se haya seleccionado año y sucursales
    const year = document.getElementById('year-select').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')];

    if (!year) {
        mostrarAlertaAdvertencia('Por favor, seleccione un año.');
        return;
    }

    if (sucursales.length === 0) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Cargar datos
    cargarGraficasVentasAnuales();
    cargarGraficasMarcasYModelos();
    mostrarMensajeExito('Ventas anuales actualizadas correctamente');
}

function buscarDatosFacturacion() {
    console.log('Buscando datos de facturación...');

    // Validar que se haya seleccionado año
    const year = document.getElementById('year-select-facturacion').value;

    if (!year) {
        mostrarAlertaAdvertencia('Por favor, seleccione un año.');
        return;
    }

    // Cargar datos
    cargarGraficasFacturacion();
    mostrarMensajeExito('Datos de facturación actualizados correctamente');
}

// ===== FUNCIONES AUXILIARES PARA VALIDACIONES =====
function validarSeleccionSucursales() {
    const sucursales = document.querySelectorAll('input[name="sucursal"]:checked');
    return sucursales.length > 0;
}

function validarSeleccionFecha() {
    const fecha = document.getElementById('fecha-hoy').value;
    return fecha && fecha.trim() !== '';
}

function validarSeleccionAnio() {
    const year = document.getElementById('year-select').value;
    return year && year.trim() !== '';
}

// ===== FUNCIONES DE SWEETALERT2 =====
function mostrarAlerta(tipo, titulo, mensaje) {
    Swal.fire({
        icon: tipo, // 'success', 'error', 'warning', 'info'
        title: titulo,
        text: mensaje,
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#007bff'
    });
}

function mostrarAlertaError(mensaje) {
    mostrarAlerta('error', 'Error', mensaje);
}

function mostrarAlertaAdvertencia(mensaje) {
    mostrarAlerta('warning', 'Atención', mensaje);
}

function mostrarAlertaExito(mensaje) {
    mostrarAlerta('success', 'Éxito', mensaje);
}

// ===== FUNCIONES DE FORMATO =====
function formatearPesos(valor) {
    if (valor === null || valor === undefined || isNaN(valor)) {
        return '$0.00';
    }

    const numero = parseFloat(valor);
    return new Intl.NumberFormat('es-MX', {
        style: 'currency',
        currency: 'MXN',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(numero);
}

function formatearPesosCorto(valor) {
    if (valor === null || valor === undefined || isNaN(valor)) {
        return '$0';
    }

    const numero = parseFloat(valor);
    if (numero >= 1000000) {
        return '$' + (numero / 1000000).toFixed(1) + 'M';
    } else if (numero >= 1000) {
        return '$' + (numero / 1000).toFixed(1) + 'K';
    } else {
        return '$' + numero.toFixed(0);
    }
}

// ===== FUNCIONES PARA EVENTOS DE CAMBIO =====
function onSucursalChange() {
    console.log('Sucursal seleccionada cambiada');
    // Opcional: Auto-actualizar si el usuario lo desea
    // actualizarTodasLasGraficas();
}

function onYearChange() {
    console.log('Año seleccionado cambiado');
    // Opcional: Auto-actualizar si el usuario lo desea
    // actualizarTodasLasGraficas();
}

function onFechaChange() {
    console.log('Fecha seleccionada cambiada');
    // Opcional: Auto-actualizar si el usuario lo desea
    // cargarGraficasIngresosDiarios();
}

// ===== FUNCIONES DE UTILIDAD PARA INDICADORES DE CARGA =====
function mostrarIndicadorCarga(selector, mensaje = 'Cargando gráfica...') {
    const elemento = document.querySelector(selector);
    if (elemento) {
        elemento.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p class="mt-2 text-muted fw-bold">${mensaje}</p>
                    <div class="progress mt-2" style="height: 4px; width: 200px; margin: 0 auto;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        `;
    }
}

function ocultarIndicadorCarga(selector) {
    const elemento = document.querySelector(selector);
    if (elemento) {
        elemento.innerHTML = '';
    }
}

// ===== INDICADORES ESPECÍFICOS PARA CADA TIPO DE GRÁFICA =====
function mostrarIndicadorIngresos() {
    mostrarIndicadorCarga('#graficaSucursal', 'Cargando ingresos por sucursal...');
    mostrarIndicadorCarga('#graficaTipoPago', 'Cargando pagos por tipo...');
    mostrarIndicadorCarga('#deudaTotal', 'Cargando deuda por sucursal...');
}

function mostrarIndicadorVentas() {
    mostrarIndicadorCarga('#sumaMontos', 'Cargando ventas mensuales...');
    mostrarIndicadorCarga('#sumaPagos', 'Cargando pagos por sucursal...');
}

function mostrarIndicadorMarcas() {
    mostrarIndicadorCarga('#recuentoMarcas', 'Cargando ventas por marca...');
    mostrarIndicadorCarga('#tratamientoGrafica', 'Cargando modelos y tratamientos...');
}

function mostrarIndicadorFacturacion() {
    mostrarIndicadorCarga('#estatus', 'Cargando facturación por estatus...');
    mostrarIndicadorCarga('#tio', 'Cargando facturación por tipo...');
    mostrarIndicadorCarga('#Sumaim', 'Cargando facturación mensual...');
}

function mostrarIndicadorError(selector, mensaje = 'Error al cargar datos') {
    const elemento = document.querySelector(selector);
    if (elemento) {
        elemento.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 350px;">
                <div class="text-center">
                    <i class="fa fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <p class="mt-3 text-muted">${mensaje}</p>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="actualizarTodasLasGraficas()">
                        <i class="fa fa-refresh me-1"></i> Reintentar
                    </button>
                </div>
            </div>
        `;
    }
}

// ===== FUNCIONES PARA BARRA DE PROGRESO GLOBAL =====
function mostrarBarraProgreso() {
    // Crear barra de progreso si no existe
    if (!document.getElementById('dashboard-progress-bar')) {
        const progressHTML = `
            <div id="dashboard-progress-container" class="position-fixed top-0 start-0 w-100" style="z-index: 9999; background: rgba(255,255,255,0.95); padding: 15px;">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <h6 id="progress-text" class="text-center mb-2">Iniciando carga del dashboard...</h6>
                            <div class="progress" style="height: 8px;">
                                <div id="dashboard-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('afterbegin', progressHTML);
    }
}

function actualizarProgreso(porcentaje, mensaje) {
    const progressBar = document.getElementById('dashboard-progress-bar');
    const progressText = document.getElementById('progress-text');

    if (progressBar) {
        progressBar.style.width = porcentaje + '%';
        progressBar.setAttribute('aria-valuenow', porcentaje);
    }

    if (progressText) {
        progressText.textContent = mensaje;
    }
}

function ocultarBarraProgreso() {
    const container = document.getElementById('dashboard-progress-container');
    if (container) {
        container.remove();
    }
}

function mostrarMensajeExito(mensaje) {
    // Crear toast de éxito
    const toastHTML = `
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 10000;">
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <i class="fa fa-check-circle me-2"></i>
                    <strong class="me-auto">Dashboard</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${mensaje}
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', toastHTML);

    // Auto-remover después de 3 segundos
    setTimeout(() => {
        const toast = document.querySelector('.toast-container');
        if (toast) {
            toast.remove();
        }
    }, 3000);
}
   
 // Obtener categorías
    function obtenerCategorias() {
        const clases = [...document.querySelectorAll('input[name="clase"]:checked')].map(c => c.value);
        const params = new URLSearchParams();
        clases.forEach(c => params.append('clases[]', c));
        fetch(`{{ path('almacen-obtener-subcategoria') }}?${params.toString()}`)
            .then(res => res.text())
            .then(html => {
                const subcategoriasElement = document.getElementById('subcategorias');
                if (subcategoriasElement) {
                    subcategoriasElement.innerHTML = html;
                    obtenerMarcas();
                }
            })
            .catch(error => console.error('Error al obtener categorías:', error));
    }
 // Obtener tags
    function getTags() {
        const enterpriseId = document.getElementById('idempresa').value;
        fetch(`{{ path('almacen-get-tags') }}?enterpriseId=${enterpriseId}`)
            .then(res => res.text())
            .then(html => {
                const el = document.getElementById('tags');
                if (el) el.innerHTML = html;
            })
            .catch(error => console.error('Error al obtener tags:', error));
    }
    // Obtener clases
    function obtenerClases() {
        const idempresa = document.getElementById('idempresa').value;
        fetch(`{{ path('almacen-obtener-clase') }}?idempresa=${idempresa}`)
            .then(res => res.text())
            .then(html => {
                const clasesElement = document.getElementById('clases');
                if (clasesElement) {
                    clasesElement.innerHTML = html;
                    obtenerCategorias();
                }
            })
            .catch(error => console.error('Error al obtener clases:', error));
    }
    // Obtener sucursales
    function obtenerSucursales() {
        const idempresa = document.getElementById('idempresa').value;
        fetch(`{{ path('almacen-obtener-sucursal') }}?idempresa=${idempresa}`)
            .then(res => res.text())
            .then(html => {
                const sucursalesElement = document.getElementById('sucursales');
                if (sucursalesElement) {
                    sucursalesElement.innerHTML = html;
                    obtenerCategorias();
                }
            })
            .catch(error => console.error('Error al obtener sucursales:', error));
    }
     function cargaDefiltros() {
        const idempresa = document.getElementById('idempresa').value;
        if (idempresa > 0) {
            obtenerSucursales();
            obtenerClases();
            getTags();
        } else {
            ['clases', 'subcategorias', 'marcas', 'sucursales'].forEach(id => {
                document.getElementById(id).innerHTML = ''; // Limpia el contenido de los filtros
            });
        }
    }
      // Cambia el estado de los checkboxes
      function toggleChecks(name, source) {
  document.querySelectorAll(`input[name="${name}"]`).forEach(cb => cb.checked = source.checked);
}
    jQuery(function ($) {
            $('#fecha-inicio-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });

            $('#fecha-fin-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });
        });
     function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }
 // Cargar resumen de ingresos
    window.cargarResumenIngresos = function () {
        const todayDate = document.getElementById('fecha-hoy').value;
        const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');
        if (!todayDate || !sucursales) return;

        $.get('/cliente-api/get-ingreso-diario-overview', { todayDate, sucursales }, function (response) {
            // Validar si hay datos válidos
            const ventaData = response.sumaVentaCobrar?.totalVenta;
            const porCobrarData = response.sumaVentaCobrar?.porCobrar;
            const cobradoData = response.sumaPagos?.totalCobrado;

            // Función para mostrar valor o mensaje de sin datos
            function mostrarValor(valor, elemento) {
                if (valor === null || valor === undefined || isNaN(parseFloat(valor))) {
                    $(elemento).text('Sin información');
                } else {
                    const numero = parseFloat(valor);
                    $(elemento).text(formatearPesos(numero));
                }
            }

            mostrarValor(ventaData, '#ventas-total');
            mostrarValor(porCobrarData, '#por-cobrar-total');
            mostrarValor(cobradoData, '#pagos-total');
        }).fail(function() {
            // En caso de error en la petición
            $('#ventas-total').text('Sin información');
            $('#por-cobrar-total').text('Sin información');
            $('#pagos-total').text('Sin información');
        });
    };
    $('#fecha-hoy, input[name="sucursal"]').on('change', function () {
        cargarResumenIngresos();
       // cargarGraficasDetalles();
    });
    document.addEventListener('DOMContentLoaded', () => {
    // Establecer las fechas al día de hoy al cargar la página
    setTodayDate();

    // Escuchar los cambios de fechas
    const fechaHoy = document.getElementById('fecha-hoy');
    if (fechaHoy) {
        fechaHoy.addEventListener('change', () => {
            refreshGraphs();
        });
    }

    // Escuchar cambios en sucursales o otros filtros
    document.querySelectorAll('input[name="sucursal"], input[name="clase"]').forEach(input => {
        input.addEventListener('change', () => {
            refreshGraphs();
        });
    });
});

// Establece las fechas de "hoy" en los campos correspondientes
function setTodayDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;
    const formattedDateText = `${day}/${month}/${year}`; // Formato para campos de texto

    console.log('Estableciendo fecha de hoy:', formattedDate);

    // Establecer fecha en todos los campos de fecha
    const fechaHoy = document.getElementById('fecha-hoy');
    const fechaInicio = document.getElementById('fecha-inicio-rango-dia');
    const fechaFin = document.getElementById('fecha-fin-rango-dia');

    if (fechaHoy) fechaHoy.value = formattedDate; // Campo tipo date
    if (fechaInicio) fechaInicio.value = formattedDateText; // Campo tipo text
    if (fechaFin) fechaFin.value = formattedDateText; // Campo tipo text
}

// ===== FUNCIÓN PARA REFRESCAR TODAS LAS GRÁFICAS =====
function refreshGraphs() {
    console.log('Refrescando todas las gráficas...');

    // Validar datos básicos antes de refrescar
    if (!validarSeleccionAnio()) {
        alert('Por favor, seleccione un año antes de refrescar.');
        return;
    }

    if (!validarSeleccionSucursales()) {
        alert('Por favor, seleccione al menos una sucursal antes de refrescar.');
        return;
    }

    // Usar el sistema de carga secuencial
    actualizarTodasLasGraficas();
}
// ===== FUNCIONES PARA CARGAR GRÁFICAS DE INGRESOS DIARIOS =====
function cargarGraficasIngresosDiarios() {
    const startDate = document.getElementById('fecha-inicio-rango-dia').value;
    const endDate = document.getElementById('fecha-fin-rango-dia').value;
    const startDateFormatted = formatDate(startDate);
    const endDateFormatted = formatDate(endDate);
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

    console.log('Cargando gráficas de ingresos diarios...');
    console.log('Rango de fechas:', startDateFormatted, 'a', endDateFormatted);
    console.log('Sucursales seleccionadas:', sucursales);

    // Validación de campos
    if (!startDateFormatted || !endDateFormatted || !sucursales) {
        alert('Por favor, seleccione las fechas y las sucursales.');
        return;
    }

    // Mostrar indicadores de carga
    mostrarIndicadorCarga('#graficaSucursal');
    mostrarIndicadorCarga('#graficaTipoPago');
    mostrarIndicadorCarga('#deudaTotal');

    $.get(`/cliente-api/get-ingreso-diario-details?startDate=${startDateFormatted}&endDate=${endDateFormatted}&sucursales=${sucursales}`, function (response) {
        // Gráfica de Ingresos por Sucursal (Pie Chart)
        cargarGraficaIngresosPorSucursal(response.dataPerSucursal);

        // Gráfica de Pagos por Tipo (Bar Chart Horizontal)
        cargarGraficaPagosPorTipo(response.dataPerPaymentType);

        // Gráfica de Deuda por Sucursal (Bar Chart Vertical)
        cargarGraficaDeudaPorSucursal(response.dataPerSucursal);

    }).fail(function (error) {
        console.error('Error al cargar gráficas de ingresos diarios:', error);
        mostrarAlertaError('Hubo un error al obtener los datos. Por favor, intente de nuevo.');
        ocultarIndicadorCarga('#graficaSucursal');
        ocultarIndicadorCarga('#graficaTipoPago');
        ocultarIndicadorCarga('#deudaTotal');
    });
}

function cargarGraficaIngresosPorSucursal(data) {
    if (!data || data.length === 0) {
        document.querySelector("#graficaSucursal").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de ingresos disponible</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#graficaSucursal');
        return;
    }

    // Filtrar datos válidos
    const datosValidos = data.filter(item => {
        const total = parseFloat(item.totalPagado);
        return !isNaN(total) && total > 0;
    });

    if (datosValidos.length === 0) {
        document.querySelector("#graficaSucursal").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin ingresos registrados para el período seleccionado</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#graficaSucursal');
        return;
    }

    const labels = datosValidos.map(item => item.sucursal || 'Sin nombre');
    const series = datosValidos.map(item => parseFloat(item.totalPagado));

    const chart = new ApexCharts(document.querySelector("#graficaSucursal"), {
        series: series,
        chart: {
            type: 'pie',
            height: 550,
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        labels: labels,
        title: {
            text: 'Ingresos por Sucursal',
            align: 'center',
            style: { fontSize: '16px', fontWeight: 'bold' }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val.toFixed(1)}%\n${formatearPesosCorto(value)}`;
            },
            style: {
                fontSize: '12px',
                fontWeight: 'bold',
                colors: ['#fff'],
                textOutline: '1px #000'
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        },
        legend: {
            position: 'bottom',
            fontSize: '12px',
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val}: ${formatearPesosCorto(value)}`;
            }
        },
        colors: ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0', '#546E7A', '#26a69a', '#D10CE8'],
        responsive: [{
            breakpoint: 768,
            options: {
                chart: { height: 400 },
                legend: { position: 'bottom' }
            }
        }]
    });

    ocultarIndicadorCarga('#graficaSucursal');
    chart.render();
}

function cargarGraficaPagosPorTipo(data) {
    console.log('=== PAGOS POR TIPO - DEBUG ===');
    console.log('Datos recibidos para pagos por tipo:', data);
    console.log('Tipo de datos:', typeof data);
    console.log('Es array:', Array.isArray(data));

    if (!data || data.length === 0) {
        document.querySelector("#graficaTipoPago").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de pagos disponible</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#graficaTipoPago');
        return;
    }

    // Mapear tipos de pago a nombres más claros en español
    function traducirTipoPago(tipo) {
        const traducciones = {
            'convenio': 'Convenio',
            'efectivo': 'Efectivo',
            'tarjeta débito': 'Tarjeta Débito',
            'tarjeta de crédito': 'Tarjeta Crédito',
            'transferencia': 'Transferencia',
            'pago descuento nomina': 'Descuento Nómina',
            'depósito': 'Depósito',
            'prestación': 'Prestación',
            'vales': 'Vales',
            // Fallbacks para otros formatos
            'cash': 'Efectivo',
            'card': 'Tarjeta',
            'credit': 'Crédito',
            'transfer': 'Transferencia',
            'check': 'Cheque',
            'tarjeta': 'Tarjeta',
            'credito': 'Crédito',
            'cheque': 'Cheque'
        };
        return traducciones[tipo?.toLowerCase()] || tipo || 'Otro';
    }

    // Procesar datos con la estructura exacta que llega: {paymentType: "...", cobrado: "..."}
    const datosValidos = data.filter(item => {
        const valor = parseFloat(item.cobrado || 0);
        const tipo = item.paymentType;
        return !isNaN(valor) && valor > 0 && tipo;
    }).map(item => ({
        tipo: item.paymentType,
        valor: parseFloat(item.cobrado)
    }));

    console.log('Datos válidos de pagos procesados:', datosValidos);
    console.log('Cantidad de datos válidos:', datosValidos.length);

    if (datosValidos.length === 0) {
        document.querySelector("#graficaTipoPago").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 500px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin datos de pagos válidos para mostrar</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#graficaTipoPago');
        return;
    }

    const labels = datosValidos.map(item => traducirTipoPago(item.tipo));
    const series = datosValidos.map(item => item.valor);

    console.log('Labels finales de tipos de pago:', labels);
    console.log('Series finales de tipos de pago:', series);
    console.log('Configuración xaxis.categories:', labels);

    const chart = new ApexCharts(document.querySelector("#graficaTipoPago"), {
        series: [{ name: 'Total Cobrado', data: series }],
        chart: {
            type: 'bar',
            height: 500,
            toolbar: { show: true },
            fontFamily: 'Arial, sans-serif'
        },
        title: { text: 'Pagos por Tipo', align: 'center' },
        plotOptions: {
            bar: {
                horizontal: true,
                borderRadius: 6,
                dataLabels: { position: 'right' },
                barHeight: '60%',
                distributed: false
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val),
            offsetX: 5,
            style: { fontSize: '12px', colors: ['#304758'], fontWeight: 'bold' }
        },
        xaxis: {
            categories: labels,
            labels: {
                style: {
                    fontSize: '13px',
                    fontWeight: 'bold',
                    colors: ['#333']
                },
                maxWidth: 150
            }
        },
        yaxis: {
            labels: {
                formatter: val => formatearPesosCorto(val)
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        },
        colors: ['#008FFB']
    });

    ocultarIndicadorCarga('#graficaTipoPago');
    chart.render();
    console.log('Gráfica de Pagos por Tipo renderizada con categorías:', labels);
}

function cargarGraficaDeudaPorSucursal(data) {
    const labels = data.map(item => item.sucursal);
    const series = data.map(item => {
        const total = parseFloat(item.totalDeuda);
        return isNaN(total) || total < 0 ? 0 : total;
    });

    const chart = new ApexCharts(document.querySelector("#deudaTotal"), {
        series: [{ name: 'Total Deuda', data: series }],
        chart: { type: 'bar', height: 450 },
        title: { text: 'Deuda por Sucursal', align: 'center' },
        plotOptions: {
            bar: {
                borderRadius: 4,
                dataLabels: { position: 'top' }
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val),
            offsetY: -20,
            style: { fontSize: '12px', colors: ['#304758'] }
        },
        xaxis: {
            categories: labels,
            labels: { style: { fontSize: '12px' } }
        },
        yaxis: {
            labels: {
                formatter: val => formatearPesosCorto(val)
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        }
    });

    ocultarIndicadorCarga('#deudaTotal');
    chart.render();
}
// Función para convertir la fecha al formato YYYY-MM-DD
function formatDate(date) {
    const [day, month, year] = date.split('/');
    return `${year}-${month}-${day}`;  // Convierte al formato YYYY-MM-DD
}
// ===== FUNCIÓN PARA POBLAR SELECT DE AÑOS =====
function poblarSelectAnios() {
    const selectVentas = document.getElementById('year-select');
    const selectFacturacion = document.getElementById('year-select-facturacion');
    const anioActual = new Date().getFullYear();
    const anioInicio = 2020;

    // Función para poblar un select específico
    function poblarSelect(select) {
        if (!select) return;

        // Limpiar opciones anteriores
        select.innerHTML = '';

        for (let anio = anioActual; anio >= anioInicio; anio--) {
            const opcion = document.createElement('option');
            opcion.value = anio;
            opcion.textContent = anio;
            if (anio === anioActual) {
                opcion.selected = true;
            }
            select.appendChild(opcion);
        }
    }

    // Poblar ambos selects
    poblarSelect(selectVentas);
    poblarSelect(selectFacturacion);
}
// Ejecutar cuando cargue el DOM
document.addEventListener('DOMContentLoaded', () => {
    poblarSelectAnios();

    // Cargar gráficas por defecto con el año actual
    setTimeout(() => {
        cargarGraficasVentasAnuales();
    }, 1000);

    // Escuchar cambios en el select de año de ventas
    const yearSelectVentas = document.getElementById('year-select');
    if (yearSelectVentas) {
        yearSelectVentas.addEventListener('change', () => {
            cargarGraficasVentasAnuales();
            cargarGraficasMarcasYModelos();
        });
    }

    // Escuchar cambios en el select de año de facturación
    const yearSelectFacturacion = document.getElementById('year-select-facturacion');
    if (yearSelectFacturacion) {
        yearSelectFacturacion.addEventListener('change', () => {
            cargarGraficasFacturacion();
        });
    }
});

// ===== FUNCIONES PARA CARGAR GRÁFICAS DE VENTAS ANUALES =====
function cargarGraficasVentasAnuales() {
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');
    const year = document.getElementById('year-select').value;

    if (!year || !sucursales) {
        alert('Por favor, seleccione un año y las sucursales.');
        return;
    }

    console.log('Cargando gráficas de ventas anuales...');
    console.log('Año seleccionado:', year);
    console.log('Sucursales seleccionadas:', sucursales);

    // Mostrar indicadores de carga
    mostrarIndicadorCarga('#sumaMontos');
    mostrarIndicadorCarga('#sumaPagos');

    $.get(`/cliente-api/get-ingreso-anual-overview?year=${year}&sucursales=${sucursales}`, function (response) {
        // Gráfica de Ventas Mensuales (Bar Chart)
        cargarGraficaVentasMensuales(response.dataPerMonth);

        // Gráfica de Pagos por Sucursal (Pie Chart)
        cargarGraficaPagosPorSucursal(response.dataPerBranch);

    }).fail(function (error) {
        console.error('Error al cargar gráficas de ventas anuales:', error);
        alert('Hubo un error al obtener los datos. Por favor, intente de nuevo.');
        ocultarIndicadorCarga('#sumaMontos');
        ocultarIndicadorCarga('#sumaPagos');
    });
}

function cargarGraficaVentasMensuales(data) {
    const nombresMeses = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
                          'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

    const ordenados = data.sort((a, b) => parseInt(a.ventaMonth) - parseInt(b.ventaMonth));
    const labels = ordenados.map(item => nombresMeses[parseInt(item.ventaMonth) - 1] || 'Sin Mes');
    const series = ordenados.map(item => parseFloat(item.cobrado));

    const chart = new ApexCharts(document.querySelector("#sumaMontos"), {
        series: [{
            name: 'Ventas Mensuales',
            data: series
        }],
        chart: {
            type: 'bar',
            height: 450
        },
        title: { text: 'Ventas Mensuales', align: 'center' },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: false
            }
        },
        colors: ['#4CAF50'],
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val)
        },
        xaxis: {
            categories: labels
        },
        yaxis: {
            labels: {
                formatter: value => formatearPesosCorto(value)
            }
        },
        tooltip: {
            y: {
                formatter: val => formatearPesos(val)
            }
        }
    });

    ocultarIndicadorCarga('#sumaMontos');
    chart.render();
}

function cargarGraficaPagosPorSucursal(data) {
    const labels = data.map(item => item.sucursal);
    const series = data.map(item => {
        const total = parseFloat(item.cobrado);
        return isNaN(total) || total < 0 ? 0 : total;
    });

    const chart = new ApexCharts(document.querySelector("#sumaPagos"), {
        series: series,
        chart: { type: 'pie', height: 450 },
        labels: labels,
        title: { text: 'Pagos por Sucursal', align: 'center' },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val.toFixed(1)}%\n${formatearPesosCorto(value)}`;
            },
            style: { fontSize: '12px', fontWeight: 'bold', colors: ['#fff'] }
        },
        legend: {
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val}: ${formatearPesosCorto(value)}`;
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        },
        responsive: [{
            breakpoint: 480,
            options: { chart: { height: 350 }, legend: { position: 'bottom' } }
        }]
    });

    ocultarIndicadorCarga('#sumaPagos');
    chart.render();
}
// ===== FUNCIONES PARA CARGAR GRÁFICAS DE MARCAS Y MODELOS =====
function cargarGraficasMarcasYModelos() {
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');
    const year = document.getElementById('year-select').value;

    if (!year || !sucursales) {
        alert('Por favor, seleccione un año y las sucursales.');
        return;
    }

    console.log('Cargando gráficas de marcas y modelos...');
    console.log('Año:', year, 'Sucursales:', sucursales);

    // Mostrar indicadores de carga
    mostrarIndicadorCarga('#recuentoMarcas');
    mostrarIndicadorCarga('#tratamientoGrafica');

    $.get(`/cliente-api/get-ingreso-anual-details?year=${year}&sucursales=${sucursales}`, function (response) {
        // Cargar gráfica de marcas
        cargarGraficaVentasPorMarca(response.marcas);

        // Cargar gráfica de modelos y tratamientos
        cargarGraficaModelosYTratamientos(response.dtm);

    }).fail(function (error) {
        console.error('Error al cargar gráficas de marcas y modelos:', error);
        alert('Hubo un error al obtener los datos. Por favor, intente de nuevo.');
        ocultarIndicadorCarga('#recuentoMarcas');
        ocultarIndicadorCarga('#tratamientoGrafica');
    });
}

function cargarGraficaVentasPorMarca(data) {
    console.log('=== VENTAS POR MARCA - DEBUG ===');
    console.log('Datos recibidos para ventas por marca:', data);
    console.log('Tipo de datos:', typeof data);
    console.log('Es array:', Array.isArray(data));

    if (!data || data.length === 0) {
        document.querySelector("#recuentoMarcas").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de marcas disponible</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#recuentoMarcas');
        return;
    }

    // Procesar datos con la estructura exacta que llega: {marca: "...", vendidos: ...}
    const datosValidos = data.filter(item => {
        const vendidos = parseInt(item.vendidos) || 0;
        const marca = item.marca;
        return vendidos > 0 && marca;
    }).map(item => ({
        marca: item.marca,
        vendidos: parseInt(item.vendidos)
    }));

    console.log('Datos válidos de marcas procesados:', datosValidos);

    if (datosValidos.length === 0) {
        document.querySelector("#recuentoMarcas").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin datos de ventas válidos</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#recuentoMarcas');
        return;
    }

    // Limitar a las 15 marcas más vendidas para evitar amontonamiento
    const datosLimitados = datosValidos
        .sort((a, b) => b.vendidos - a.vendidos)
        .slice(0, 15);

    const labels = datosLimitados.map(item => item.marca);
    const series = datosLimitados.map(item => item.vendidos);

    console.log('Labels finales de marcas (top 15):', labels);
    console.log('Series finales de marcas (top 15):', series);
    console.log('Configuración xaxis.categories para marcas:', labels);

    function formatearUnidades(val) {
        if (val >= 1000000) {
            return (val / 1000000).toFixed(1) + ' millones';
        } else if (val >= 1000) {
            return (val / 1000).toFixed(1) + ' mil';
        } else {
            return val.toLocaleString() + ' uds';
        }
    }

    const chart = new ApexCharts(document.querySelector("#recuentoMarcas"), {
        series: [{ name: 'Unidades Vendidas', data: series }],
        chart: {
            type: 'bar',
            height: 550,
            toolbar: {
                show: true,
                tools: {
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            zoom: {
                enabled: true,
                type: 'x',
                autoScaleYaxis: true
            },
            fontFamily: 'Arial, sans-serif'
        },
        title: { text: 'Ventas por Marca', align: 'center' },
        plotOptions: {
            bar: {
                horizontal: true,
                borderRadius: 6,
                dataLabels: { position: 'right' },
                barHeight: '65%'
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearUnidades(val),
            offsetX: 8,
            style: {
                fontSize: '12px',
                colors: ['#304758'],
                fontWeight: 'bold'
            }
        },
        xaxis: {
            categories: labels,
            labels: {
                style: {
                    fontSize: '12px',
                    fontWeight: '600',
                    colors: ['#333']
                },
                maxWidth: 180
            }
        },
        yaxis: {
            labels: {
                formatter: val => formatearUnidades(val)
            }
        },
        tooltip: {
            y: { formatter: val => formatearUnidades(val) + ' vendidas' }
        }
    });

    ocultarIndicadorCarga('#recuentoMarcas');
    chart.render();
    console.log('Gráfica de Ventas por Marca renderizada con categorías:', labels);
}

function cargarGraficaModelosYTratamientos(data) {
    console.log('Datos recibidos para modelos y tratamientos:', data);

    if (!data || data.length === 0) {
        document.querySelector("#tratamientoGrafica").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de modelos disponible</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#tratamientoGrafica');
        return;
    }

    // Procesar datos con la estructura exacta que llega: {modelo: "...", vendidos: ...}
    const datosValidos = data.filter(item => {
        const vendidos = parseInt(item.vendidos) || 0;
        const modelo = item.modelo;
        return vendidos > 0 && modelo;
    }).map(item => ({
        modelo: item.modelo,
        vendidos: parseInt(item.vendidos)
    }));

    console.log('Datos válidos de modelos procesados:', datosValidos);

    if (datosValidos.length === 0) {
        document.querySelector("#tratamientoGrafica").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin datos de modelos válidos</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#tratamientoGrafica');
        return;
    }

    // Limitar a los 20 modelos más vendidos para evitar amontonamiento
    const datosLimitados = datosValidos
        .sort((a, b) => b.vendidos - a.vendidos)
        .slice(0, 20);

    const labels = datosLimitados.map(item => item.modelo);
    const series = datosLimitados.map(item => item.vendidos);

    console.log('Labels finales de modelos (top 20):', labels);
    console.log('Series finales de modelos (top 20):', series);

    function formatearUnidades(val) {
        if (val >= 1000000) {
            return (val / 1000000).toFixed(1) + ' millones';
        } else if (val >= 1000) {
            return (val / 1000).toFixed(1) + ' mil';
        } else {
            return val.toLocaleString() + ' uds';
        }
    }

    const chart = new ApexCharts(document.querySelector("#tratamientoGrafica"), {
        series: [{ name: 'Unidades Vendidas', data: series }],
        chart: {
            type: 'bar',
            height: 550,
            toolbar: {
                show: true,
                tools: {
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            zoom: {
                enabled: true,
                type: 'x',
                autoScaleYaxis: true
            },
            fontFamily: 'Arial, sans-serif'
        },
        title: { text: 'Modelos y Tratamientos', align: 'center' },
        plotOptions: {
            bar: {
                horizontal: true,
                borderRadius: 6,
                dataLabels: { position: 'right' },
                barHeight: '65%'
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearUnidades(val),
            offsetX: 8,
            style: {
                fontSize: '12px',
                colors: ['#304758'],
                fontWeight: 'bold'
            }
        },
        xaxis: {
            categories: labels,
            labels: {
                style: {
                    fontSize: '12px',
                    fontWeight: '600',
                    colors: ['#333']
                },
                maxWidth: 200
            }
        },
        yaxis: {
            labels: {
                formatter: val => formatearUnidades(val)
            }
        },
        tooltip: {
            y: { formatter: val => formatearUnidades(val) + ' vendidas' }
        }
    });

    ocultarIndicadorCarga('#tratamientoGrafica');
    chart.render();
}
// ===== FUNCIONES PARA CARGAR GRÁFICAS DE FACTURACIÓN =====
function cargarGraficasFacturacion() {
    const year = document.getElementById('year-select-facturacion').value;

    if (!year) {
        alert('Por favor, seleccione un año.');
        return;
    }

    console.log('Cargando gráficas de facturación para el año:', year);

    // Mostrar indicadores de carga
    mostrarIndicadorCarga('#estatus');
    mostrarIndicadorCarga('#tio');
    mostrarIndicadorCarga('#Sumaim');

    // Cargar datos de facturación general
    cargarDatosFacturacionGeneral(year);

    // Cargar datos de facturación detallada
    cargarDatosFacturacionDetallada(year);
}

function cargarDatosFacturacionGeneral(year) {
    $.get(`/cliente-api/get-facturacion-uam-anual-overview?year=${year}`, function (response) {
        // Cargar gráfica de facturación por estatus
        cargarGraficaFacturacionPorEstatus(response.sumaEstatus);

        // Actualizar total de facturación
        let totalFacturacion = 0;
        if (response.suma && Array.isArray(response.suma) && response.suma.length > 0) {
            totalFacturacion = parseFloat(response.suma[0].importe || 0);
        } else if (response.suma && typeof response.suma === 'object' && response.suma.importe) {
            totalFacturacion = parseFloat(response.suma.importe || 0);
        } else if (response.suma && !isNaN(parseFloat(response.suma))) {
            totalFacturacion = parseFloat(response.suma);
        }

        if (totalFacturacion > 0) {
            $('#suma-im').text(formatearPesos(totalFacturacion));
        } else {
            $('#suma-im').text('Sin información');
        }

    }).fail(function (error) {
        console.error('Error al cargar datos de facturación general:', error);
        mostrarAlertaError('Hubo un error al obtener los datos de facturación.');
        ocultarIndicadorCarga('#estatus');
    });
}

function cargarGraficaFacturacionPorEstatus(data) {
    console.log('Datos recibidos para facturación por estatus:', data);

    if (!data || data.length === 0) {
        document.querySelector("#estatus").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de facturación por estatus</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#estatus');
        return;
    }

    // Mapear estatus a nombres más claros
    function traducirEstatus(estatus) {
        const traducciones = {
            'paid': 'Pagado',
            'pending': 'Pendiente',
            'cancelled': 'Cancelado',
            'overdue': 'Vencido',
            'draft': 'Borrador',
            'pagado': 'Pagado',
            'pendiente': 'Pendiente',
            'cancelado': 'Cancelado',
            'vencido': 'Vencido',
            'borrador': 'Borrador'
        };
        return traducciones[estatus?.toLowerCase()] || estatus || 'Sin Estatus';
    }

    // Filtrar datos válidos con soporte para diferentes estructuras
    const datosValidos = data.filter(item => {
        const importe = parseFloat(item.importe || item.total || item.amount || 0);
        return !isNaN(importe) && importe > 0;
    }).map(item => ({
        estatus: item.estatus || item.status || item.estado || 'Sin Estatus',
        importe: parseFloat(item.importe || item.total || item.amount || 0)
    }));

    console.log('Datos válidos procesados para estatus:', datosValidos);

    if (datosValidos.length === 0) {
        document.querySelector("#estatus").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin datos de facturación válidos</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#estatus');
        return;
    }

    const labels = datosValidos.map(item => traducirEstatus(item.estatus));
    const series = datosValidos.map(item => item.importe);

    console.log('Labels de estatus:', labels);
    console.log('Series de estatus:', series);

    const chart = new ApexCharts(document.querySelector("#estatus"), {
        series: [{ name: 'Importe Facturado', data: series }],
        chart: {
            type: 'bar',
            height: 500,
            toolbar: {
                show: true,
                tools: {
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            }
        },
        title: {
            text: 'Facturación por Estatus',
            align: 'center',
            style: { fontSize: '16px', fontWeight: 'bold' }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                borderRadius: 6,
                dataLabels: { position: 'top' },
                columnWidth: '60%'
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val),
            offsetY: -20,
            style: { fontSize: '12px', colors: ['#304758'], fontWeight: 'bold' }
        },
        xaxis: {
            categories: labels,
            labels: {
                style: { fontSize: '12px', fontWeight: 'bold' },
                rotate: -45
            }
        },
        yaxis: {
            labels: {
                formatter: val => formatearPesosCorto(val)
            }
        },
        tooltip: {
            y: {
                formatter: val => `${formatearPesos(val)} facturado`
            }
        },
        colors: ['#28a745', '#ffc107', '#dc3545', '#6c757d', '#17a2b8'],
        grid: {
            padding: {
                bottom: 20
            }
        }
    });

    ocultarIndicadorCarga('#estatus');
    chart.render();
}

    


    
function cargarDatosFacturacionDetallada(year) {
    const nombresMeses = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
                          'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

    $.get(`/cliente-api/get-facturacion-uam-anual-details?year=${year}`, function (response) {
        // Cargar gráfica de facturación mensual
        cargarGraficaFacturacionMensual(response.sumaMes, nombresMeses);

        // Cargar gráfica de facturación por tipo
        cargarGraficaFacturacionPorTipo(response.sumaClase);

    }).fail(function (error) {
        console.error('Error al cargar datos de facturación detallada:', error);
        alert('Hubo un error al obtener los datos detallados de facturación.');
        ocultarIndicadorCarga('#Sumaim');
        ocultarIndicadorCarga('#tio');
    });
}

function cargarGraficaFacturacionMensual(data, nombresMeses) {
    const ordenados = data.sort((a, b) => parseInt(a.mes) - parseInt(b.mes));
    const labels = ordenados.map(item => nombresMeses[parseInt(item.mes) - 1] || 'Sin Mes');
    const series = ordenados.map(item => parseFloat(item.importe));

    const chart = new ApexCharts(document.querySelector("#Sumaim"), {
        series: [{
            name: 'Facturación Mensual',
            data: series
        }],
        chart: {
            type: 'bar',
            height: 450
        },
        title: { text: 'Facturación Mensual', align: 'center' },
        plotOptions: {
            bar: {
                borderRadius: 6,
                horizontal: false
            }
        },
        colors: ['#008FFB'],
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val),
            style: { fontSize: '12px' }
        },
        xaxis: {
            categories: labels
        },
        yaxis: {
            labels: {
                formatter: val => formatearPesosCorto(val)
            }
        },
        tooltip: {
            y: {
                formatter: val => formatearPesos(val)
            }
        }
    });

    ocultarIndicadorCarga('#Sumaim');
    chart.render();
}

function cargarGraficaFacturacionPorTipo(data) {
    const labels = data.map(item => item.tipo);
    const series = data.map(item => parseFloat(item.importe));

    const chart = new ApexCharts(document.querySelector("#tio"), {
        series: series,
        chart: {
            type: 'donut',
            height: 450
        },
        labels: labels,
        title: { text: 'Facturación por Tipo', align: 'center' },
        plotOptions: {
            pie: {
                startAngle: -90,
                endAngle: 270
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${formatearPesosCorto(value)} (${val.toFixed(1)}%)`;
            }
        },
        fill: {
            type: 'gradient'
        },
        legend: {
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val}: ${formatearPesosCorto(value)}`;
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: { width: 250 },
                legend: { position: 'bottom' }
            }
        }]
    });

    ocultarIndicadorCarga('#tio');
    chart.render();
}

</script>
{% endblock %}
