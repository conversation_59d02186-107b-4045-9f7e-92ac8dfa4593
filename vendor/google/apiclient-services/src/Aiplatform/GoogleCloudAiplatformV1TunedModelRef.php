<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1TunedModelRef extends \Google\Model
{
  /**
   * @var string
   */
  public $pipelineJob;
  /**
   * @var string
   */
  public $tunedModel;
  /**
   * @var string
   */
  public $tuningJob;

  /**
   * @param string
   */
  public function setPipelineJob($pipelineJob)
  {
    $this->pipelineJob = $pipelineJob;
  }
  /**
   * @return string
   */
  public function getPipelineJob()
  {
    return $this->pipelineJob;
  }
  /**
   * @param string
   */
  public function setTunedModel($tunedModel)
  {
    $this->tunedModel = $tunedModel;
  }
  /**
   * @return string
   */
  public function getTunedModel()
  {
    return $this->tunedModel;
  }
  /**
   * @param string
   */
  public function setTuningJob($tuningJob)
  {
    $this->tuningJob = $tuningJob;
  }
  /**
   * @return string
   */
  public function getTuningJob()
  {
    return $this->tuningJob;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1TunedModelRef::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1TunedModelRef');
