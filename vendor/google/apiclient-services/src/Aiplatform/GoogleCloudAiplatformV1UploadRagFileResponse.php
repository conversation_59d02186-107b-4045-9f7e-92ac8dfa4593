<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1UploadRagFileResponse extends \Google\Model
{
  protected $errorType = GoogleRpcStatus::class;
  protected $errorDataType = '';
  protected $ragFileType = GoogleCloudAiplatformV1RagFile::class;
  protected $ragFileDataType = '';

  /**
   * @param GoogleRpcStatus
   */
  public function setError(GoogleRpcStatus $error)
  {
    $this->error = $error;
  }
  /**
   * @return GoogleRpcStatus
   */
  public function getError()
  {
    return $this->error;
  }
  /**
   * @param GoogleCloudAiplatformV1RagFile
   */
  public function setRagFile(GoogleCloudAiplatformV1RagFile $ragFile)
  {
    $this->ragFile = $ragFile;
  }
  /**
   * @return GoogleCloudAiplatformV1RagFile
   */
  public function getRagFile()
  {
    return $this->ragFile;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1UploadRagFileResponse::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1UploadRagFileResponse');
