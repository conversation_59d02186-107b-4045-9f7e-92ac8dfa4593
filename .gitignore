+###> symfony/framework-bundle ###
/.env.local.php
/.env.*.local
/.env.dev
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/public/uploads/
/public/codigos/
/public/uploads/*
/var/
###< symfony/framework-bundle ###

###> symfony/phpunit-bridge ###
.phpunit
/phpunit.xml
###< symfony/phpunit-bridge ###
node_modules

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
.idea/php.xml
.idea/phpunit.xml
###< phpunit/phpunit ###

###> phpstorm ###
.idea/
/PhpStorm-*.tar.gz
###< phpstorm ###

###> lexik/jwt-authentication-bundle ###
/config/jwt/*.pem
###< lexik/jwt-authentication-bundle ###
.env
documentacion-cambios-detallada.md
documentacion-correcciones.md
documentacion-cambios.md

# Ignore migration backups
src/Migrations_backup/
